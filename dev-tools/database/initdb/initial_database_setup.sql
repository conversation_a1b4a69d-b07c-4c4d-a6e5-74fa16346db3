CREATE SCHEMA IF NOT EXISTS security;
GRANT ALL PRIVILEGES ON SCHEMA security TO application_user;

CREATE SCHEMA IF NOT EXISTS fvm;
GRANT ALL PRIVILEGES ON SCHEMA fvm TO application_user;

CREATE SCHEMA IF NOT EXISTS vehicle;
GRANT ALL PRIVILEGES ON SCHEMA vehicle TO application_user;

CREATE SCHEMA IF NOT EXISTS vehicleregistration;
GRANT ALL PRIVILEGES ON SCHEMA vehicleregistration TO application_user;

DROP SCHEMA IF EXISTS performancetesting;

CREATE SCHEMA IF NOT EXISTS  vehiclelocation;
GRANT ALL PRIVILEGES ON SCHEMA vehiclelocation TO application_user;

CREATE SCHEMA IF NOT EXISTS  jobs;
GRANT ALL PRIVILEGES ON SCHEMA jobs TO application_user;

CREATE SCHEMA IF NOT EXISTS vehiclehistory;
GRANT ALL PRIVILEGES  ON SCHEMA vehiclehistory TO application_user;

CREATE SCHEMA IF NOT EXISTS  emh_user;
GRANT ALL PRIVILEGES ON SCHEMA emh_user TO application_user;

CREATE SCHEMA IF NOT EXISTS  vehicletransfer;
GRANT ALL PRIVILEGES ON SCHEMA vehicletransfer TO application_user;

CREATE SCHEMA IF NOT EXISTS  predelivery;
GRANT ALL PRIVILEGES ON SCHEMA predelivery TO application_user;

CREATE SCHEMA IF NOT EXISTS  consigneedatasheet;
GRANT ALL PRIVILEGES ON SCHEMA consigneedatasheet TO application_user;

CREATE SCHEMA IF NOT EXISTS  vehiclecampaigns;
GRANT ALL PRIVILEGES ON SCHEMA vehiclecampaigns TO application_user;

CREATE SCHEMA IF NOT EXISTS  emh_fleet_data;
GRANT ALL PRIVILEGES ON SCHEMA emh_fleet_data TO application_user;

CREATE SCHEMA IF NOT EXISTS  noncustomeradequate;
GRANT ALL PRIVILEGES ON SCHEMA noncustomeradequate TO application_user;

CREATE SCHEMA IF NOT EXISTS  vehiclesales;
GRANT ALL PRIVILEGES ON SCHEMA vehiclesales TO application_user;

CREATE SCHEMA IF NOT EXISTS balancesheet;
GRANT ALL PRIVILEGES ON SCHEMA balancesheet TO application_user;

CREATE SCHEMA IF NOT EXISTS fms_migration;
GRANT ALL PRIVILEGES ON SCHEMA fms_migration TO application_user;

CREATE SCHEMA IF NOT EXISTS tirechangeappointment;
GRANT ALL PRIVILEGES ON SCHEMA tirechangeappointment TO application_user;
