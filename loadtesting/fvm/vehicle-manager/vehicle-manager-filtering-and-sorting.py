import os
import logging
import traceback

from locust import HttpUser, task, events
from playwright.sync_api import sync_playwright

HOST = "https://mobilityservicesdev.porsche.services/"
RESPONSE_TIME_THRESHOLD_IN_MS = 400
TASK_TO_MONITOR = "Vehicle Manager: Fetch data with no filter applied"

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

def check_response(response):
    try:
        if response.headers.get('Content-Type') != 'application/json' and response.json()["data"] is None:
            logging.error("Unexpected response received")
            response.failure("Unexpected response received")
    except Exception as e:
        handle_exception(e, "Response validation failed", response)

def handle_exception(e, error_message, response):
    logging.error(f"{error_message}: {e}\n{traceback.format_exc()}")
    response.failure(f"{error_message}: {e}")

def automate_login_and_get_cookies(email, password):
    with sync_playwright() as p:
        # Launch browser in headless mode (background mode)
        browser = p.chromium.launch(headless=True)  # Set to False for debugging
        context = browser.new_context()

        # Open a new page
        page = context.new_page()

        # Step 1: Navigate to EMH login page
        page.goto(HOST)
        page.click("p-button#login-button")

        # Step 2: Enter email/username
        page.fill("input[type='email']", email)
        page.click("input[type='submit']")

        # Step 3: Wait for password field to load and enter password
        page.wait_for_selector("input[type='password']")
        page.fill("input[type='password']", password)
        page.click("input[type='submit']")

        # Step 4: Wait for redirection to the target application
        with page.expect_navigation():
            page.locator('input[type=submit]').click()

        # Step 5: Retrieve AWS ALB cookies
        cookies = context.cookies()
        alb_cookie_dict = {}
        for cookie in cookies:
            if "AWSELBAuthSessionCookie" in cookie['name']:
                alb_cookie_dict[cookie['name']] = cookie['value']

    # Close the browser
        browser.close()

        return alb_cookie_dict


def search_with_four_filters_in_four_domains_and_one_sort():
    # test filter performance in tables that the vehicle-master-data
    # has a JOIN clause with
    return {
        "startRow": 0,
        "endRow": 50,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicle.model.description": {
                "filterType": "text",
                "type": "contains",
                "filter": "1"
            },
            "vehicle.production.factory": {
                "filterType": "multi",
                "filterModels": [
                    {
                        "filterType": "text",
                        "type": "contains",
                        "filter": "150"
                    },
                    None
                ]
            }
        },
        "sortModel": [{
            "sort": "asc",
            "colId": "vehicle.vguid"
        }]
    }


def search_with_filter_returning_large_number_of_results_and_one_sort():
    # a large number of results with an IN query will
    # mean that the query planner will have to adjust
    # its join strategy to provide good performance
    return {
        "startRow": 0,
        "endRow": 50,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicle.model.description": {
                "filterType": "text",
                "type": "contains",
                "filter": "Macan"
            },
            "vehicle.equipmentNumber": {
                "filterType": "number",
                "type": "blank"
            }
        },
        "sortModel": [
            {
                "sort": "desc",
                "colId": "vehicle.consumption.driveType"
            }
        ]
    }


def search_with_filter_having_null_condition_and_sort():
    # a filter on a null condition will not hit any
    # existing indexes unless a partial index covering NULLs
    # is provided. The additional sort should make the query slower
    # the sort columns are not indexed
    return {
        "startRow": 0,
        "endRow": 50,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleRegistration.testNumber": {
                "filterType": "number",
                "type": "blank"
            }
        },
        "sortModel": [
            {
                "sort": "asc",
                "colId": "vehicle.model.orderType"
            }
        ]
    }


def search_with_filter_having_null_condition():
    # a filter on a null condition will not hit any
    # existing indexes unless a partial index covering NULLs
    # is provided.
    return {
        "startRow": 0,
        "endRow": 50,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {
            "vehicleRegistration.testNumber": {
                "filterType": "number",
                "type": "blank"
            }
        },
        "sortModel": []
    }


def search_with_no_filter():
    # search with no filter
    return {
        "startRow": 0,
        "endRow": 50,
        "rowGroupCols": [],
        "valueCols": [],
        "pivotCols": [],
        "pivotMode": False,
        "groupKeys": [],
        "filterModel": {},
        "sortModel": []
    }


_email = os.getenv('AZURE_AD_TECHNICAL_USER_NAME')
_password = os.getenv('AZURE_AD_TECHNICAL_USER_SECRET')
cookies = automate_login_and_get_cookies(_email, _password)


class DomainUser(HttpUser):

    global ENV

    def on_start(self):
        """
        Set ENV when the test starts.
        """
        global ENV
        ENV = self.environment

    @task
    def read_vehicle_with_search_with_four_filters_in_four_domains_and_one_sort(self):
        try:
            with self.client.post(
                    "/api/vs/ui/vehicles/search",
                    name="Vehicle Manager: Fetch data with filter on 4 domain fields And 1 sort",
                    cookies=cookies,
                    headers={
                        "x-trace-id": "locust-load-test-search_with_four_filters_in_four_domains_and_one_sort"
                    },
                    json=search_with_four_filters_in_four_domains_and_one_sort(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e,"Failed to fetch data", response)

    @task(3)
    def read_vehicle_with_no_filter(self):
        try:
            with self.client.post(
                    "/api/vs/ui/vehicles/search",
                    name="Vehicle Manager: Fetch data with no filter applied",
                    cookies=cookies,
                    headers={
                        "x-trace-id": "locust-load-test-search_with_no_filter"
                    },
                    json=search_with_no_filter(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e, "Failed to fetch data", response)

    @task
    def read_vehicle_with_filter_returning_large_number_of_results_and_one_sort(self):
        try:
            with self.client.post(
                    "/api/vs/ui/vehicles/search",
                    name="Vehicle Manager: Fetch data with contains filter and 1 sort",
                    cookies=cookies,
                    headers={
                        "x-trace-id": "locust-load-test-search_with_filter_returning_large_number_of_results_and_one_sort"
                    },
                    json=search_with_filter_returning_large_number_of_results_and_one_sort(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e,"Failed to fetch data", response)

    @task
    def read_vehicle_with_filter_having_null_condition_and_sort(self):
        try:
            with self.client.post(
                    "/api/vs/ui/vehicles/search",
                    name="Vehicle Manager: Fetch data with filter applied on null fields and 1 sort",
                    cookies=cookies,
                    headers={
                        "x-trace-id": "locust-load-test-search_with_filter_having_null_condition_and_sort"
                    },
                    json=search_with_filter_having_null_condition_and_sort(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e,"Failed to fetch data", response)

    @task
    def read_vehicle_with_filter_having_null_condition(self):
        try:
            with self.client.post("/api/vs/ui/vehicles/search",
                    name="Vehicle Manager: Fetch data with filter applied on null fields",
                    cookies=cookies,
                    headers={
                        "x-trace-id": "locust-load-test-search_with_filter_having_null_condition"
                    },
                    json=search_with_filter_having_null_condition(),
                    catch_response=True
            ) as response:
                check_response(response)
        except Exception as e:
            handle_exception(e,"Failed to fetch data", response)


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    # Get 95th percentile response time for TASK_TO_MONITOR
    response_time_95 = ENV.runner.stats.get(TASK_TO_MONITOR, "POST").get_response_time_percentile(0.95)
    logging.info(f"95th Percentile Response Time: {response_time_95} ms")

    # Check if there are any failed requests
    total_failures = ENV.runner.stats.total.num_failures
    logging.info(f"Total Failures: {total_failures}")

    if total_failures > 0 or response_time_95 > RESPONSE_TIME_THRESHOLD_IN_MS:
        logging.error("Test Failed")
        ENV.process_exit_code = 1
    else:
        logging.info("Test Passed")

