package com.fleetmanagement.modules.masterdata.dto

import com.fleetmanagement.modules.masterdata.utils.LicensePlateUtility
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import java.time.OffsetDateTime
import kotlin.jvm.optionals.getOrNull

/**
 * # Kafka Message DTO: Schema Compatibility Guide
 *
 * This class defines a Kafka message. Its JSON Schema is auto-generated
 * and registered in Schema Registry. Follow these rules to avoid breaking
 * consumers.
 *
 * ## Safe Changes (Compatible):
 * - Add new fields as nullable (`val newField: Type?`) – no `@field:NotNull`.
 * - Make existing fields nullable.
 * - Deprecate fields: make nullable + mark with `@Deprecated`.
 *
 * ## Breaking Changes (Avoid):
 * - Removing or renaming fields.
 * - Changing a field's type (e.g., `String` → `Int`).
 * - Making a nullable field required.
 *
 * ## Notes:
 * - `@field:NotNull` → required in schema.
 * - Nullable types → optional in schema.
 *
 * ## Please update the README example json if you change the data product
 */
data class ExternalVehicleRegistration(
    val firstRegistrationDate: OffsetDateTime? = null,
    val currentLicensePlate: String? = null,
    val currentLicensePlateSuffix: String? = null, // Char is not compatible with JSON Schema
    val testNumber: Long? = null,
    val hsn: String? = null,
    val tsn: String? = null,
    val sfme: Boolean?,
) {
    companion object {
        fun from(registrationData: VehicleRegistrationOrder?): ExternalVehicleRegistration? =
            registrationData?.let {
                val fullLicensePlate = registrationData.licencePlate?.getOrNull()
                val (licensePlate, suffix) =
                    fullLicensePlate?.let {
                        LicensePlateUtility.splitLicensePlate(
                            fullLicensePlate,
                        )
                    } ?: Pair(null, null)

                ExternalVehicleRegistration(
                    firstRegistrationDate =
                        registrationData.firstRegistrationDate
                            ?.getOrNull()
                            ?.toOffsetDateTime(),
                    currentLicensePlate = licensePlate,
                    currentLicensePlateSuffix = suffix,
                    testNumber = registrationData.testNumber?.getOrNull(),
                    hsn = registrationData.hsn?.getOrNull(),
                    tsn = registrationData.tsn?.getOrNull(),
                    sfme = registrationData.sfme?.getOrNull(),
                )
            }
    }
}
