package com.fleetmanagement.modules.masterdata.dto

import com.fleetmanagement.emhshared.LeasingArt
import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.masterdata.utils.LicensePlateUtility
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationPeriod
import java.time.OffsetDateTime
import java.util.UUID
import javax.validation.constraints.NotNull

/**
 * # Kafka Message DTO: Schema Compatibility Guide
 *
 * This class defines a Kafka message. Its JSON Schema is auto-generated
 * and registered in Schema Registry. Follow these rules to avoid breaking
 * consumers.
 *
 * ## Safe Changes (Compatible):
 * - Add new fields as nullable (`val newField: Type?`) – no `@field:NotNull`.
 * - Make existing fields nullable.
 * - Deprecate fields: make nullable + mark with `@Deprecated`.
 *
 * ## Breaking Changes (Avoid):
 * - Removing or renaming fields.
 * - Changing a field's type (e.g., `String` → `Int`).
 * - Making a nullable field required.
 *
 * ## Notes:
 * - `@field:NotNull` → required in schema.
 * - Nullable types → optional in schema.
 *
 * ## Please update the README example json if you change the data product
 */
data class ExternalVehicleTransferWithStatus(
    @NotNull
    val status: String,
    val transfers: List<ExternalVehicleTransfer> = emptyList(),
)

data class ExternalVehicleTransfer(
    val vehicleResponsiblePerson: ExternalVehicleResponsiblePerson?,
    val deliveryIndex: Int?,
    @NotNull
    val vehicleTransferKey: Long,
    val deliveryDate: OffsetDateTime?,
    val returnDate: OffsetDateTime?,
    val leasingPrivilege: String?,
    val vehicleUsage: ExternalVehicleUsage?,
    val usageGroup: ExternalUsageGroup?,
    val licensePlates: List<LicensePlatePeriod> = emptyList(),
    val mileageAtDelivery: Int?,
    val mileageAtReturn: Int?,
    val utilizationArea: String?,
    val maintenanceOrderNumber: String?,
    val usingCostCenter: String?,
    val depreciationRelevantCostCenterId: UUID?,
    val internalOrderNumber: String?,
    val plannedDeliveryDate: OffsetDateTime?,
    val leasingArt: ExternalLeasingArt?,
)

data class LicensePlatePeriod(
    val licensePlate: String,
    val licensePlateSuffix: String? = null,
    val fromDate: OffsetDateTime?,
    val toDate: OffsetDateTime?,
) {
    companion object {
        fun from(period: RegistrationPeriod): LicensePlatePeriod {
            val (licensePlate, suffix) = LicensePlateUtility.splitLicensePlate(period.licencePlate)
            return LicensePlatePeriod(
                licensePlate = licensePlate,
                licensePlateSuffix = suffix,
                fromDate = period.fromDate?.toOffsetDateTime(),
                toDate = period.toDate?.toOffsetDateTime(),
            )
        }
    }
}

data class ExternalVehicleUsage(
    val usage: String?,
)

data class ExternalUsageGroup(
    val description: String?,
)

data class ExternalVehicleResponsiblePerson(
    val employeeNumber: String?,
    val department: String?,
) {
    companion object {
        fun from(personDetail: VehiclePersonDetail?) =
            personDetail?.let {
                ExternalVehicleResponsiblePerson(
                    employeeNumber = it.employeeNumber,
                    department = it.department,
                )
            }
    }
}

data class ExternalLeasingArt(
    val id: String,
    val description: String,
    val pool: Boolean,
) {
    companion object {
        fun calculate(
            vehicleUsage: String?,
            vehicleType: VehicleType?,
            manufacturer: String?,
        ): ExternalLeasingArt? {
            if (vehicleUsage.isNullOrBlank() || (vehicleType == null) || manufacturer.isNullOrBlank()) {
                return null
            }
            val leasingArt = LeasingArt.of(vehicleUsage, vehicleType, manufacturer)
            return ExternalLeasingArt(
                id = leasingArt.id,
                description = leasingArt.description,
                pool = leasingArt.isPool,
            )
        }
    }
}
