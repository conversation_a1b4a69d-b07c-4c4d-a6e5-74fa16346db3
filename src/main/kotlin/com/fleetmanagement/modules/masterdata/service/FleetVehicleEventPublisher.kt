/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.masterdata.service

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.masterdata.configuration.FleetVehicleMasterDataConfiguration
import com.fleetmanagement.modules.masterdata.configuration.FleetVehicleMasterDataConfigurationProperties
import com.fleetmanagement.modules.masterdata.dto.ExternalConsumptionInfo
import com.fleetmanagement.modules.masterdata.dto.ExternalCurrentMileage
import com.fleetmanagement.modules.masterdata.dto.ExternalFleetInfo
import com.fleetmanagement.modules.masterdata.dto.ExternalFleetMasterVehicle
import com.fleetmanagement.modules.masterdata.dto.ExternalLeasingArt
import com.fleetmanagement.modules.masterdata.dto.ExternalOrderInfo
import com.fleetmanagement.modules.masterdata.dto.ExternalUsageGroup
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicle
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleColorData
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleEvaluationData
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleModel
import com.fleetmanagement.modules.masterdata.dto.ExternalVehiclePrice
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleProduction
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleRegistration
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleResponsiblePerson
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleSalesData
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleTechnicalData
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleTransfer
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleTransferWithStatus
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleUsage
import com.fleetmanagement.modules.masterdata.dto.ExternalVehicleWltpInfo
import com.fleetmanagement.modules.masterdata.dto.LicensePlatePeriod
import com.fleetmanagement.modules.masterdata.entities.FleetVehicleMasterData
import com.fleetmanagement.modules.masterdata.entities.domain.VehicleEventType
import com.fleetmanagement.modules.vehicledata.api.ReadMileageReadings
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonException
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import com.fleetmanagement.modules.vehiclesales.api.ReadVehicleSaleByVehicleId
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleSalesDto
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleSalesNotFoundException
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferAbstractionDto
import com.fleetmanagement.modules.vehicletransfer.application.port.VehicleTransferAbstractionUsecase
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferStatus
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.security.MessageDigest
import java.time.OffsetDateTime
import java.util.UUID

@Service
@ConditionalOnBean(FleetVehicleMasterDataConfiguration::class)
class FleetVehicleEventPublisher(
    private val producer: StreamzillaKafkaProducer<ExternalFleetMasterVehicle>,
    private val config: FleetVehicleMasterDataConfigurationProperties,
    private val readVehicle: ReadVehicleByVehicleId,
    private val readVehicleTransfer: VehicleTransferAbstractionUsecase,
    private val readRegistration: ReadRegistrationOrder,
    private val readMileageReadings: ReadMileageReadings,
    private val readVehicleSaleByVehicleId: ReadVehicleSaleByVehicleId,
    private val readVehiclePersonDetail: ReadVehiclePersonDetailByEmployeeNumber,
    private val deduplicator: FleetVehicleEventDeduplicator,
    private val objectMapper: ObjectMapper,
) {
    private val log = LoggerFactory.getLogger(FleetVehicleEventPublisher::class.java)

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun publishKafkaEvent(vehicle: FleetVehicleMasterData) {
        try {
            val vehicleData =
                readVehicle.readVehicleById(vehicle.vehicleId)
                    ?: throw NoSuchElementException("No vehicle with id ${vehicle.vehicleId}")

            val messageKey =
                vehicleData.vguid ?: vehicleData.vin ?: throw IllegalStateException("Vehicle must have a vguid or vin")
            val hashedMessageKey = hashKey(messageKey)
            val headers = prepareHeaders(vehicleData, vehicle.eventType)
            val payload = preparePayload(vehicle, vehicleData)
            publishNewEvent(vehicle, payload, hashedMessageKey, headers)
        } catch (ex: Exception) {
            vehicle.markAsFailed()
            log.error("Failed to publish snapshot for vehicle ${vehicle.vehicleId}: ${ex.message}", ex)
        } finally {
            vehicle.markAsProcessed()
        }
    }

    private fun publishNewEvent(
        vehicle: FleetVehicleMasterData,
        payload: ExternalFleetMasterVehicle,
        hashedMessageKey: String,
        headers: Map<String, String?>,
    ) {
        if (deduplicator.shouldPublishEvent(vehicle.vehicleId, vehicle.eventType, payload)) {
            producer.send(config.topic, hashedMessageKey, payload, headers)
            deduplicator.replaceHash(vehicle.vehicleId, vehicle.eventType, payload)
            log.info("Successfully published fleet vehicle snapshot for vehicle ${vehicle.vehicleId}")
        } else {
            log.info("Duplicate event with id ${vehicle.vehicleId} and type ${vehicle.eventType} is ignored")
        }
    }

    private fun preparePayload(
        vehicle: FleetVehicleMasterData,
        vehicleData: VehicleDTO,
    ): ExternalFleetMasterVehicle {
        val vehicleTransferData = readVehicleTransfer.findVehicleTransfersBy(vehicle.vehicleId)
        val vehicleRegistrationData = readRegistration.getLatestOrderBy(vehicle.vehicleId).data
        val currentMileage = getCurrentMileage(vehicleData.id)
        val salesData = getVehicleSalesData(vehicleData.id)
        return prepareKafkaEvent(
            vehicleData = vehicleData,
            vehicleTransferData = vehicleTransferData,
            registrationData = vehicleRegistrationData,
            currentMileage = currentMileage,
            salesData = salesData,
        )
    }

    private fun hashKey(input: String): String {
        val bytes = MessageDigest.getInstance("SHA-256").digest(input.toByteArray())
        return bytes.joinToString("") { "%02x".format(it) }
    }

    private fun prepareKafkaEvent(
        vehicleData: VehicleDTO,
        vehicleTransferData: List<VehicleTransferAbstractionDto>,
        registrationData: VehicleRegistrationOrder?,
        currentMileage: MileageReadingDTO?,
        salesData: VehicleSalesDto?,
    ): ExternalFleetMasterVehicle =
        ExternalFleetMasterVehicle(
            vehicle =
                ExternalVehicle(
                    vguid = vehicleData.vguid,
                    vin = vehicleData.vin,
                    equiId = vehicleData.equiId,
                    equipmentNumber = vehicleData.equipmentNumber,
                    status = vehicleData.status,
                    production = ExternalVehicleProduction.from(vehicleData.production),
                    model = ExternalVehicleModel.from(vehicleData.model),
                    price = ExternalVehiclePrice.from(vehicleData.price),
                    registration = ExternalVehicleRegistration.from(registrationData),
                    transfers =
                        prepareVehicleTransfers(
                            vehicleTransferData = vehicleTransferData,
                            vehicleType = vehicleData.model?.vehicleType,
                            manufacturer = vehicleData.model?.manufacturer,
                        ),
                    wltp = ExternalVehicleWltpInfo.from(vehicleData.wltpInfo),
                    consumption = ExternalConsumptionInfo.from(vehicleData.consumption),
                    currentMileage = ExternalCurrentMileage.from(currentMileage),
                    order = ExternalOrderInfo.from(vehicleData.order),
                    fleet = ExternalFleetInfo.from(vehicleData.fleet),
                    evaluation = ExternalVehicleEvaluationData.from(vehicleData.evaluation),
                    sales = ExternalVehicleSalesData.from(salesData),
                    technical = ExternalVehicleTechnicalData.from(vehicleData.technical),
                    color = ExternalVehicleColorData.from(vehicleData.color),
                    options = jsonNodeToMap(vehicleData.options),
                ),
        )

    private fun jsonNodeToMap(jsonNode: JsonNode?): Map<String, Any>? =
        jsonNode?.let {
            objectMapper.convertValue(it, object : TypeReference<Map<String, Any>>() {})
        }

    private fun getVehicleSalesData(vehicleId: UUID): VehicleSalesDto? =
        try {
            readVehicleSaleByVehicleId.readVehicleSalesBy(vehicleId)
        } catch (e: VehicleSalesNotFoundException) {
            log.warn("No sales data found for vehicle with id $vehicleId: ${e.message}")
            null
        }

    private fun getCurrentMileage(vehicleId: UUID): MileageReadingDTO? =
        readMileageReadings.readMileageReadings(vehicleId).maxByOrNull { it.readDate }

    private fun prepareVehicleTransfers(
        vehicleTransferData: List<VehicleTransferAbstractionDto>,
        vehicleType: VehicleType?,
        manufacturer: String?,
    ): List<ExternalVehicleTransferWithStatus> =
        vehicleTransferData.groupBy { it.status }.map { (status, transfers) ->
            ExternalVehicleTransferWithStatus(
                status = status,
                transfers =
                    transfers.map { transfer ->
                        ExternalVehicleTransfer(
                            vehicleResponsiblePerson =
                                transfer.vehicleResponsiblePerson?.let {
                                    ExternalVehicleResponsiblePerson.from(getPersonDetails(it))
                                },
                            deliveryIndex = transfer.deliveryIndex,
                            vehicleTransferKey = transfer.vehicleTransferKey.value,
                            deliveryDate = transfer.deliveryDate,
                            returnDate = transfer.returnDate,
                            leasingPrivilege = transfer.leasingPrivilege,
                            vehicleUsage = ExternalVehicleUsage(usage = transfer.vehicleUsage?.usage),
                            usageGroup = ExternalUsageGroup(description = transfer.usageGroup?.description),
                            licensePlates = prepareRegistrationPeriods(transfer),
                            mileageAtDelivery = transfer.mileageAtDelivery,
                            mileageAtReturn = transfer.mileageAtReturn,
                            utilizationArea = transfer.utilizationArea?.name,
                            maintenanceOrderNumber = transfer.maintenanceOrderNumber,
                            usingCostCenter = transfer.usingCostCenter?.value,
                            depreciationRelevantCostCenterId = transfer.depreciationRelevantCostCenterId?.value,
                            internalOrderNumber = transfer.internalOrderNumber,
                            plannedDeliveryDate = transfer.plannedDeliveryDate,
                            leasingArt =
                                ExternalLeasingArt.calculate(
                                    vehicleUsage = transfer.vehicleUsage?.usage,
                                    vehicleType = vehicleType,
                                    manufacturer = manufacturer,
                                ),
                        )
                    },
            )
        }

    private fun getPersonDetails(employeeNumber: EmployeeNumber): VehiclePersonDetail? =
        try {
            readVehiclePersonDetail.readVehiclePersonDetailByEmployeeNumber(employeeNumber.value)
        } catch (e: ReadVehiclePersonException) {
            log.warn("No person details found for employee number ${employeeNumber.value}: ${e.message}")
            null
        }

    private fun prepareRegistrationPeriods(transfer: VehicleTransferAbstractionDto): List<LicensePlatePeriod> {
        if ((transfer.status != VehicleTransferStatus.ACTIVE.name && transfer.status != VehicleTransferStatus.FINISHED.name) ||
            transfer.deliveryDate == null
        ) {
            return emptyList()
        }

        return readRegistration
            .getRegistrationPeriodByVehicleId(transfer.vehicleId, transfer.deliveryDate, transfer.returnDate)
            .data
            .map {
                LicensePlatePeriod.from(it)
            }
    }

    private fun prepareHeaders(
        vehicleData: VehicleDTO,
        eventType: VehicleEventType,
    ) = mapOf(
        "vguid" to vehicleData.vguid,
        "vin" to vehicleData.vin,
        "equipmentNumber" to vehicleData.equipmentNumber?.toString(),
        "timestamp" to OffsetDateTime.now().toString(),
        "eventType" to eventType.name,
    )
}
