package com.fleetmanagement.modules.masterdata.utils

import com.fleetmanagement.modules.vehicledata.api.validators.isValidGermanLicensePlate

object LicensePlateUtility {
    fun splitLicensePlate(fullLicensePlate: String): Pair<String, String?> {
        if (!isValidGermanLicensePlate(fullLicensePlate)) {
            return Pair(fullLicensePlate, null)
        }

        val lastChar = fullLicensePlate.last()
        return if (lastChar.isLetter()) { // could be E or H
            fullLicensePlate.dropLast(1) to lastChar.toString()
        } else {
            fullLicensePlate to null
        }
    }
}
