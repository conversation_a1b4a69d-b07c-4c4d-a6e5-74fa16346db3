package com.fleetmanagement.modules.rela.adapter.out.rest

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.MediaType
import org.springframework.web.reactive.function.client.ExchangeFilterFunction
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.support.WebClientAdapter
import org.springframework.web.service.invoker.HttpServiceProxyFactory

@Configuration
@ConditionalOnProperty(name = ["rela.enabled"], havingValue = "true")
class RelaConfiguration

@Configuration
@ConditionalOnBean(RelaConfiguration::class)
class RelaClientConfiguration {
    @Bean
    @Qualifier("rela")
    fun relaServiceProxyFactory(
        @Value("\${rela.base-url}") baseUrl: String,
        @Value("\${rela.api-key}") apiKey: String,
        relaClientExceptionHandler: RelaClientExceptionHandler,
    ): HttpServiceProxyFactory {
        val webClient =
            WebClient
                .builder()
                .baseUrl(baseUrl)
                .defaultHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader("Accept", MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader("Authorization", "Rela-Token $apiKey")
                .filter(ExchangeFilterFunction.ofResponseProcessor(relaClientExceptionHandler::clientErrorResponseProcessor))
                .filter(relaClientExceptionHandler::clientErrorRequestProcessor)
                .build()

        val adapter = WebClientAdapter.create(webClient)
        return HttpServiceProxyFactory.builderFor(adapter).build()
    }

    @Bean
    @Qualifier("rela")
    fun getRelaWebClient(
        @Qualifier("rela") relaServiceProxyFactory: HttpServiceProxyFactory,
    ): RelaWebClient = relaServiceProxyFactory.createClient(RelaWebClient::class.java)
}
