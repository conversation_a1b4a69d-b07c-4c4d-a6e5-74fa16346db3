package com.fleetmanagement.modules.tirechangeappointment.application


import com.fleetmanagement.modules.rela.application.port.CancelRelaAppointment
import com.fleetmanagement.modules.rela.application.port.CreateRelaAppointment
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class TireChangeRelaSync(
    private val createRelaAppointment: CreateRelaAppointment,
    private val cancelRelaAppointment: CancelRelaAppointment,
    private val tireChangeEntryFinder: TireChangeEntryFinder
) {

    /**
     * fetch all the tire change appointments with empty relaOrderNumber and create appointment for them in rela
     */
    fun syncCreateRelaAppointments() {
        val appointmentsWithoutRelaOrderNumber = tireChangeEntryFinder.getAllEntriesWithNullRelaOrderNumber()
        appointmentsWithoutRelaOrderNumber.forEach(createRelaAppointment.createAppointment())
    }

    companion object {
        private val log = LoggerFactory.getLogger(TireChangeRelaSync::class.java)
    }
}
