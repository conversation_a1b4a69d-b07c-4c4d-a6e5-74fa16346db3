/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.domain

import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.LastModifiedBy
import java.io.Serializable
import java.time.OffsetDateTime
import java.util.UUID

@Entity
@Table(name = "tire_change_entry", schema = "tirechangeappointment")
class TireChangeEntry(
    @Column(name = "msbookings_appointment_id") val msBookingsAppointmentId: String,
    // TODO check with FPT1-1294, if RELA accepts startTime and endTime for appointment ,
    //  if yes, replace below with startTime and endTime
    @Column(name = "msbookings_appointment_date") val msBookingsAppointmentDate: OffsetDateTime,
    @Column(name = "license_plate") val licensePlate: String,
    @Column(name = "first_name") val firstName: String,
    @Column(name = "last_name") val lastName: String,
    @Column(name = "vin") val vin: String,
    @Column(name = "should_update_rela") val shouldUpdateRela: Boolean,
    @Column(name = "rela_order_number") val relaOrderNumber: String,
    // TODO (FPT1-1297) add vehicle options once its known what we need to save
) {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: TireChangeEntryId = TireChangeEntryId()

    @CreatedBy
    @Column(name = "created_by")
    var createdBy: String = ""

    @CreationTimestamp
    @Column(name = "created_date")
    var created: OffsetDateTime = OffsetDateTime.now()

    @LastModifiedBy
    @Column(name = "last_modified_by")
    var lastModifiedBy: String = ""

    @UpdateTimestamp
    @Column(name = "last_modified_date")
    var lastModified: OffsetDateTime = OffsetDateTime.now()
}

@Embeddable
data class TireChangeEntryId(
    @Basic val value: UUID = UUID.randomUUID(),
) : Serializable
