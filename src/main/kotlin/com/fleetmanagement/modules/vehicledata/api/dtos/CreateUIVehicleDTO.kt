/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.dtos

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import com.fleetmanagement.modules.vehicledata.api.domain.TireSet
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vehicledata.api.validators.ValidEngineCapacity
import com.fleetmanagement.modules.vehicledata.api.validators.ValidEnginePower
import com.fleetmanagement.modules.vehicledata.api.validators.ValidLicensePlate
import com.fleetmanagement.modules.vehicledata.api.validators.ValidRegistrationDate
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAConsumptionInfo
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAModelInfo
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAPmpData
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAPriceInfo
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAProductionInfo
import com.fleetmanagement.modules.vehicledata.repository.entities.JPATechnicalInfo
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.Pattern
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.util.UUID

@ValidRegistrationDate
@ValidEngineCapacity
@ValidEnginePower
@ValidLicensePlate
data class CreateUIVehicleDTO(
    @field:Pattern(regexp = "^.{1,17}$", message = "error.validation.pattern")
    val vin: String,
    val manufacturer: String,
    val modelDescription: String,
    val amountSeats: Int?,
    val engineCapacity: Float?,
    val licencePlate: String?,
    val registrationDate: ZonedDateTime?,
    val technicalModelYear: Int?,
    @field:Pattern(regexp = "E10|SUPER|REGULAR|DIESEL|ELECTRIC", message = "error.validation.invalid-fuel-type")
    val primaryFuelType: String?,
    @field:Pattern(regexp = "SR|WR|AR", message = "error.validation.invalid-current-tires")
    val currentTires: String?,
    val orderType: String,
    @field:Pattern(regexp = "E10|SUPER|REGULAR|DIESEL|ELECTRIC", message = "error.validation.invalid-fuel-type")
    val secondaryFuelType: String?,
    val netPriceWithExtras: BigDecimal?,
    @field:Pattern(regexp = "PKW|TRUCK|TRAILER|MOTORCYCLE|TRANSPORTER", message = "error.validation.invalid-vehicle-type")
    val vehicleType: String,
    val pmpDataOdometer: Int,
    val enginePower: Int?,
    val vehicleResponsiblePerson: String,
    val internalContactPerson: String?,
    val externalLeaseStart: ZonedDateTime?,
    val externalLeaseEnd: ZonedDateTime?,
    @field:Min(0)
    val externalLeaseRate: Float?,
    val externalLeaseLessee: String?,
    val vehicleUsageId: UUID,
    val internalOrderNumber: String?,
    @field:Pattern(regexp = "AV|UV|FE", message = "error.validation.invalid-financial-asset-type")
    val financialAssetType: String?,
    @field:Pattern(regexp = "00H001\\d{4}", message = "error.validation.pattern")
    val usingCostCenter: String?,
)

fun CreateUIVehicleDTO.toJPAVehicleEntity() =
    JPAVehicleEntity(
        source = VehicleSource.MANUAL,
        vin = this.vin,
        modelInfo =
            JPAModelInfo(
                modelDescription = this.modelDescription,
                orderType = this.orderType,
                vehicleType = VehicleType.valueOf(this.vehicleType),
            ).apply { manufacturer = <EMAIL> },
        technicalInfo =
            JPATechnicalInfo(
                amountSeats = this.amountSeats,
                engineCapacity = this.engineCapacity,
                powerkw = this.enginePower,
            ),
        consumptionInfo =
            JPAConsumptionInfo(
                primaryFuelType = this.primaryFuelType?.let { FuelType.valueOf(it) },
                secondaryFuelType = this.secondaryFuelType?.let { FuelType.valueOf(it) },
            ),
        productionInfo =
            JPAProductionInfo(
                technicalModelYear = this.technicalModelYear,
            ),
        priceInfo = JPAPriceInfo().apply { this.updateNetPriceWithExtras(netPriceWithExtras = <EMAIL>) },
        pmpData = JPAPmpData(odometer = this.pmpDataOdometer),
        currentTires = this.currentTires?.let { TireSet.valueOf(it) },
        externalLeaseStart = this.externalLeaseStart,
        externalLeaseEnd = this.externalLeaseEnd,
        externalLeaseLessee = this.externalLeaseLessee,
        externalLeaseRate = this.externalLeaseRate,
    ).apply {
        financialAssetType = <EMAIL>?.let { FinancialAssetType.valueOf(it) }
    }
