# Spring Boot and Library Configurations
server:
  port: 8080
  servlet:
    context-path: /api/vs
  compression:
    enabled: true
    mime-types: application/json
    min-response-size: 2KB
logging:
  level:
    root: INFO
    com.zaxxer.hikari: INFO

spring:
  devtools:
    restart:
      enabled: 'true'
      poll-interval: '2s'
      quiet-period: '1s'
      additional-paths:
        - src/main/kotlin/
  profiles:
    default: local
  datasource:
    hikari:
      jdbc-url: ${SPRING_DATASOURCE_URL:***********************************************}
      username: ${SPRING_DATASOURCE_USERNAME:application_user}
      password: ${SPRING_DATASOURCE_PASSWORD:Password1}
      maximum-pool-size: 30
      minimum-idle: 10
      max-lifetime: 840000 # 14 min in milseconds
      driver-class-name: org.postgresql.Driver
      idle-timeout: 300000 # 5 min in milseconds
      connection-timeout: 300
      pool-name: VSHikariCP
  jpa:
    properties:
      hibernate:
        jdbc:
          batch_size: 5
          fetch_size: 250
    open-in-view: false
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:29092}
    consumer:
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    properties:
      sasl:
        mechanism: PLAIN
        jaas:
          config: org.apache.kafka.common.security.plain.PlainLoginModule required username="${KAFKA_USER}" password="${KAFKA_PASSWORD}";
      security:
        protocol: ${KAFKA_SECURITY_PROTOCOL:PLAINTEXT}
      session:
        timeout:
          ms: 45000
  jackson:
    mapper:
      default-view-inclusion: true
  quartz:
    job-store-type: jdbc
    overwrite-existing-jobs: true
    startup-delay: 30
    jdbc:
      initialize-schema: never
    properties:
      org.quartz.jobStore:
        isClustered: true
        class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
        driverDelegateClass: org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
        tablePrefix: jobs.qrtz_
        acquireTriggersWithinLock: true
      org.quartz.scheduler:
        instanceId: AUTO
        skipUpdateCheck: true
  security:
    oauth2:
      client:
        registration:
          cap:
            client-id: ${CAP_OAUTH2_CLIENT_ID}
            client-secret: ${CAP_OAUTH2_CLIENT_SECRET}
            authorization-grant-type: client_credentials
          entra-id:
            client-id: ${ENTRA_ID_OAUTH2_CLIENT_ID:4025ad4f-5367-46cb-ae91-05e21d176efe}
            client-secret: ${ENTRA_ID_OAUTH2_CLIENT_SECRET}
            scope: ${ENTRA_ID_API_SCOPE:https://graph.microsoft.com/.default}
            authorization-grant-type: client_credentials
          vehicle-location:
            client-id: ${VEHICLE_LOCATION_API_CLIENT_ID}
            client-secret: ${VEHICLE_LOCATION_API_CLIENT_SECRET}
            scope: ${VEHICLE_LOCATION_API_SCOPE}
            authorization-grant-type: client_credentials
          dmsi:
            client-id: ${DMSI_OAUTH2_CLIENT_ID}
            client-secret: ${DMSI_OAUTH2_CLIENT_SECRET}
            authorization-grant-type: client_credentials
          pace:
            authorization-grant-type: client_credentials
            client-id: ${OAUTH2_CLIENT_ID_FOR_PACE}
            client-secret: ${OAUTH2_CLIENT_SECRET_FOR_PACE}
            #very important, PACE Auth server will only work with form parameters
            client-authentication-method: client_secret_post
          pace-balance-sheet:
            authorization-grant-type: client_credentials
            client-id: ${OAUTH2_CLIENT_ID_FOR_PACE_BALANCE_SHEET}
            client-secret: ${OAUTH2_CLIENT_SECRET_FOR_PACE_BALANCE_SHEET}
            #very important, PACE Auth server will only work with form parameters
            client-authentication-method: client_secret_post
        provider:
          vehicle-location:
            token-uri: https://login.microsoftonline.com/${VEHICLE_LOCATION_AZURE_TENANT_ID}/oauth2/v2.0/token
          cap:
            token-uri: https://${PPN_HOST}/as/token.oauth2
          dmsi:
            token-uri: https://${RWIL_IDP_HOST}/auth/realms/Porsche-Central/protocol/openid-connect/token
          entra-id:
            token-uri: https://login.microsoftonline.com/${ENTRA_ID_AZURE_TENANT_ID:07f8138c-e3e4-4b45-842b-9ecc3ba58cf4}/oauth2/v2.0/token
          pace:
            token-uri: https://${OAUTH2_HOST_PACE}/oauth/token
          pace-balance-sheet:
            token-uri: https://${OAUTH2_HOST_PACE_BALANCE_SHEET}/oauth/token
  mvc:
    async:
      request-timeout: 120000
springdoc:
  paths-to-match: /**
  api-docs:
    enabled: false
    path: /api-docs
  swagger-ui:
    enabled: false

app:
  environment: ${APP_ENVIRONMENT:TEST} # TEST, PROD
  email:
    recipient-email-address: ${APP_EMAIL_RECIPIENT_EMAIL_ADDRESS:}

fleet-master-data-publisher:
  enabled: ${ENABLE_FLEET_MASTER_DATA:true} # Will enable or disable publishing events to external systems
  producer:
    key-serializer: org.apache.kafka.common.serialization.StringSerializer
    value-serializer: io.confluent.kafka.serializers.json.KafkaJsonSchemaSerializer
    schema-registry-url: ${KAFKA_SCHEMA_REGISTRY_URL:https://psrc-8vyvr.eu-central-1.aws.confluent.cloud}
    schema-registry-user: ${KAFKA_SCHEMA_REGISTRY_USER:HPKMESFZJFBWJZ56}
    schema-registry-password: ${KAFKA_SCHEMA_REGISTRY_PASSWORD}
    auto-register-schemas: ${KAFKA_SCHEMA_AUTO_REGISTER:false}
  topic: ${FLEET_MASTER_DATA_TOPIC:FRA_emhs_fleet_master_vehicle_dev}
  scheduler:
    cron: ${FLEET_MASTER_DATA_CRON_SCHEDULE:0 * * * * ?} # Publish master data events every one minute

# Application level configuration
# These configurations are read and handled in the application code for other cross-cutting concerns (like security)

observability:
  metrics-registry: ${METRICS_REGISTRY:local} # local, cloudwatch
  cloudwatch-namespace: vehicle-service-dev
  region: ${AWS_REGION}

security:
  enabled: ${ENABLE_SECURITY:true}
  exposed-endpoints:
    user-info: true
    alb-headers: ${ENABLE_ALBHEADERS_ENDPOINT_FOR_TESTING:true}
  api-gateway-token-verification:
    issuer-uri: ${OAUTH2_ISSUER_URI:https://login.microsoftonline.com/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4/v2.0}
    jwks-uri: ${OAUTH2_JWKS_URI:https://login.microsoftonline.com/common/discovery/keys}
    audience: ${OAUTH2_VEHICLE_AUDIENCE:1d612e2b-317e-4d59-80f5-f80b3e3a33c0}
  alb-token-verification:
    expected-alb-arn: ${EXPECTED_ALB_ARN:arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/fleet-management-ui-alb/ec94b0fb4c906a17}
    public-key-host: https://public-keys.auth.elb.${AWS_REGION}.amazonaws.com
    issuer-uri: ${OAUTH2_ISSUER_URI:https://login.microsoftonline.com/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4/v2.0}
    app-id: ${OAUTH2_VEHICLE_REGISTRATION_APP_ID:cfb99f74-19b0-4747-b96f-81989c59bb4a}
    app-domain: ${APP_DOMAIN:mobilityservicesdev.porsche.services}
    logout-uri: ${OAUTH2_LOGOUT_URI:https://login.microsoftonline.com/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4/oauth2/v2.0/logout}
    logout-redirect-uri: ${LOGOUT_LANDING_PAGE:https://mobilityservicesdev.porsche.services/logout}
    toggles:
      allow-overwrite-of-groups-from-request-header: ${ALLOW_OVERWRITE_OF_GROUPS_FROM_REQUEST_HEADER:false}
  entra-id:
    enabled: ${ENABLE_ENTRA_ID_SCHEDULER:true}
    scheduler:
      # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
      # 0 0 0 * * ?
      cron: ${ENTRA_ID_SYNC_CRON_SCHEDULE:0 0 0 * * ?}
    base-url: "https://graph.microsoft.com/v1.0"
    administrative-unit-id: ${ENTRA_ID_ADMINISTRATIVE_UNIT_ID:6bbcd412-527e-4e4d-89c1-5862b4e5d239}
    app-role-assignment-id: ${ENTRA_ID_APP_ROLE_ASSIGNMENT_ID:6f9c0024-186a-43d9-aa3a-cbcb8c9067d0}

oidc:
  azure:
    token-url: ${OAUTH2_TOKEN_URI:https://login.microsoftonline.com/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4/oauth2/v2.0}
    refresh-interval-in-sec: 3600

# Application Module Configuration
vehicle-data:
  vw-oidc-token:
    uri: ${OIDC_TOKEN_URL:https://identity-sandbox.vwgroup.io/oidc/v1/token}
    refresh-interval-in-sec: 3600
  pvh:
    base-uri: ${PVH_BASE_URL:https://api.staging.emea.gravity.porsche.com/prod-business-asdcs-vehicle-service}
    client-id: ${PVH_CLIENT_ID}
    client-secret: ${PVH_CLIENT_SECRET}

    streamzilla:
      enabled: ${KAFKA_ENABLE_PVH_INGESTION:false}
      group-id: ${KAFKA_PVH_CONSUMER_GROUP_ID:FRA_emhs_pvh_Vehicle_Embargo_01_2024}
      stop-consuming-on-error: ${KAFKA_PVH_STOP_ON_ERROR:false}
      auto-offset-reset: ${KAFKA_AUTO_OFFSET_RESET:latest}
      timestamp-based-filtering:
        disable-after: ${TIMESTAMP_FILTERING_DISABLE_AFTER:2024-12-31T08:00:00Z}
  dms:
    number-of-damages:
      kafka:
        consumer:
          topic: ${NUMBER_OF_DAMAGES_TOPIC:FRA_emhs_dms_numer_of_damages_topic_dev}
          group-id: ${NUMBER_OF_DAMAGES_GROUP_ID:FRA_emhs_dms_numer_of_damages_topic_dev.consumer}
        enabled: ${NUMBER_OF_DAMAGES_TOPIC_KAFKA_CONSUMERS:false}

  pia:
    kafka:
      enabled: ${ENABLE_KAFKA_PIA_INTEGRATION:false}
      consumer:
        topic: ${KAFKA_PIA_TOPIC:FRA_one_vms_piaom_importer_invoice}
        group-id: ${KAFKA_PIA_CONSUMER_GROUP_ID:FRA_emhs_one_vms_piaom_importer_invoice_local}
        auto-offset-reset: ${KAFKA_PIA_AUTO_OFFSET_RESET:earliest}
  p40:
    kafka:
      enabled: ${ENABLE_KAFKA_P40_INTEGRATION:false}
      consumer:
        topic: ${KAFKA_FINANCIAL_ASSET_TYPE_TOPIC:FRA_account_2_report_Fleet_Vehicle_Manager_Asset_Class}
        group-id: ${KAFKA_FINANCIAL_ASSET_TYPE_CONSUMER_GROUP_ID:FRA_emhs_p40_account_2_report_fleet_vehicle_manager_asset_class_local}
        auto-offset-reset: ${KAFKA_FINANCIAL_ASSET_TYPE_AUTO_OFFSET_RESET:earliest}
  status:
    recalculation-cron: ${VEHICLE_STATUS_RECALCULATION_SCHEDULE:0 0 0 1 1 ? 2099}

  factory-car-preparation-order-number:
    factory-car-preparation-order-number-update-cron: ${FACTORY_CAR_PREPARATION_ORDER_NUMBER_UPDATE_SCHEDULE:0 0 0 1 1 ? 2099}

  financial-asset-type-update:
    financial-asset-type-update-sync-cron: ${FINANCIAL_ASSET_TYPE_UPDATE_SYNC_CRON:0 0 0 1 1 ? 2099}

fvm:
  vehicle-registration-base-uri: ${VR_BASE_URI:http://localhost:8082}
  access-control:
    history-deletion:
      scheduler:
        cron: "0 0 3 * * ?"
      retention-period: 30 # Days

fms-migration-job:
  enabled: ${FMS_MIGRATION_MODULE_ENABLED:false}
  fms-source:
    storage:
      sse: aws:kms
      sse-kms-key: ${FMS_MIGRATION_BUCKET_KMS_KEY_ID:arn:aws:kms:eu-west-1:************:key/5e4877b0-51ba-479a-9091-d12d6f818ecb}
      bucket: ${FMS_MIGRATION_BUCKET:vehicle-service-fms-migration-************}

vehicle-archive:
  enabled: ${ENABLE_ARCHIVAL_SCHEDULER:false}
  vehicle-transfer:
    storage:
      sse: aws:kms
      sse-kms-key: ${STORAGE_ARCHIVE_BUCKET_KMS_KEY_ID:arn:aws:kms:eu-west-1:************:key/5e4877b0-51ba-479a-9091-d12d6f818ecb}
      bucket: ${STORAGE_ARCHIVE_BUCKET_VEHICLE_TRANSFER:vehicle-transfer-service-archive-************}
  vehicle-data:
    storage:
      sse: aws:kms
      sse-kms-key: ${STORAGE_ARCHIVE_BUCKET_KMS_KEY_ID:arn:aws:kms:eu-west-1:************:key/5e4877b0-51ba-479a-9091-d12d6f818ecb}
      bucket: ${STORAGE_ARCHIVE_BUCKET:vehicle-service-archive-************}
  vehicle-history:
    storage:
      sse: aws:kms
      sse-kms-key: ${STORAGE_ARCHIVE_BUCKET_KMS_KEY_ID:arn:aws:kms:eu-west-1:************:key/5e4877b0-51ba-479a-9091-d12d6f818ecb}
      bucket: ${STORAGE_ARCHIVE_BUCKET_VEHICLE_HISTORY:vehicle-history-service-archive-************}
  vehicle-sales:
    storage:
      sse: aws:kms
      sse-kms-key: ${STORAGE_ARCHIVE_BUCKET_KMS_KEY_ID:arn:aws:kms:eu-west-1:************:key/5e4877b0-51ba-479a-9091-d12d6f818ecb}
      bucket: ${STORAGE_ARCHIVE_BUCKET_VEHICLE_SALES:vehicle-sales-service-archive-************}
  clients:
    vehicle-registration:
      base-url: ${VR_BASE_URL:http://localhost:8081/api/vr}
      client-id: ${VEHICLE_REGISTRATION_ARCHIVE_API_CLIENT_ID:vr-m2m-archive-client-id}
      client-secret: ${VEHICLE_REGISTRATION_ARCHIVE_API_CLIENT_SECRET:vr-m2m-archive-client-secret}
      scope: ${VEHICLE_REGISTRATION_ARCHIVE_API_SCOPE:vr-app-id}
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    # 0 0 0 1 * ?
    cron: ${VEHICLE_ARCHIVE_CRON_SCHEDULE:0 0/5 * * * ?}

vehicle-legalhold:
  storage:
    sse: aws:kms
    sse-kms-key: ${VEHICLE_LEGALHOLD_STORAGE_BUCKET_KMS_KEY_ID:arn:aws:kms:eu-west-1:************:key/5e4877b0-51ba-479a-9091-d12d6f818ecb}
  vehicle-data:
    bucket: ${VEHICLE_LEGALHOLD_STORAGE_BUCKET:vehicle-service-legal-hold-************}
  vehicle-sales:
    bucket: ${VEHICLE_LEGALHOLD_STORAGE_BUCKET_VEHICLE_SALES:vehicle-service-sales-legal-hold-************}
  clients:
    vehicle-registration:
      base-url: ${VR_BASE_URL:http://localhost:8081/api/vr}
      client-id: ${VEHICLE_REGISTRATION_LEGALHOLD_API_CLIENT_ID:vr-m2m-archive-client-id}
      client-secret: ${VEHICLE_REGISTRATION_LEGALHOLD_API_CLIENT_SECRET:vr-m2m-archive-client-secret}
      scope: ${VEHICLE_REGISTRATION_LEGALHOLD_API_SCOPE:vr-app-id}

vehicle-registration:
  client:
    base-url: ${VR_BASE_URL:http://localhost:8081/api/vr}
    client-id: ${VEHICLE_REGISTRATION_API_CLIENT_ID:vr-m2m-archive-client-id}
    client-secret: ${VEHICLE_REGISTRATION_API_CLIENT_SECRET:vr-m2m-archive-client-secret}
    scope: ${VEHICLE_REGISTRATION_API_SCOPE:vr-app-id}


vehicle-location:
  base-url: ${VEHICLE_LOCATION_BASE_URL:}
  sync-strategy: ${VEHICLE_LOCATION_SYNC_STRATEGY:startup}
  vls-kafka-integration-enabled: ${VEHICLE_LOCATION_VLS_KAFKA_INTEGRATION_ENABLED:false}
  object-location-event:
    updated:
      kafka:
        consumer:
          topic: ${OBJECT_LOCATION_UPDATED_TOPIC:FRA_emhs_vls_object_location_event_dev}
          group-id: ${OBJECT_LOCATION_UPDATED_TOPIC_GROUP_ID:FRA_emhs_vls_object_location_event.dev.consumer123}
  location-event:
    updated:
      kafka:
        consumer:
          topic: ${LOCATION_UPDATED_TOPIC:FRA_emhs_vls_location_updated_event}
          group-id: ${LOCATION_UPDATED_TOPIC_GROUP_ID:FRA_emhs_vls_location_updated_event.consumer}
    deleted:
      kafka:
        consumer:
          topic: ${LOCATION_DELETED_TOPIC:FRA_emhs_vls_location_deleted_event}
          group-id: ${LOCATION_DELETED_TOPIC_GROUP_ID:FRA_emhs_vls_location_deleted_event.consumer}

rela:
  enabled: ${RELA_ENABLED:false}
  base-url: ${RELA_BASE_URL:https://rela.sdnord.de}
  api-key: ${RELA_API_KEY}

vehicle-person:
  feature-flags:
    read-vehicle-person-enabled: ${FEATURE_FLAGS_READ_VEHICLE_PERSON_ENABLED:false}
  employee:
    base-url: ${EMPLOYEE_BASE_URL:http://localhost:8082/api}
    client-id: ${EMPLOYEE_API_CLIENT_ID:us-m2m-client-id}
    client-secret: ${EMPLOYEE_API_CLIENT_SECRET:us-m2m-client-secret}
    scope: ${EMPLOYEE_API_SCOPE:us-app-id}
    token-uri: ${EMPLOYEE_OAUTH2_TOKEN_URI:https://login.microsoftonline.com/56564e0f-83d3-4b52-92e8-a6bb9ea36564/oauth2/v2.0/token}
  cap:
    base-uri: ${CAP_BASE_URL}
    region: ${CAP_REGION}
    X-Porsche-Client-Id: ${CAP_IBM_API_GATEWAY_CLIENT_ID}
    X-Porsche-Client-Secret: ${CAP_IBM_API_GATEWAY_CLIENT_SECRET}

vehicle-transfer:
  allow-incoming-traffic: ${VEHICLE_TRANSFER_ALLOW_INCOMING_TRAFFIC:true}
  license-plate-event:
    updated:
      kafka:
        consumer:
          topic: ${LICENSE_PLATE_TOPIC:FRA_emhs_vr_license_plate_dev}
          group-id: ${LICENSE_PLATE_GROUP_ID:FRA_emhs_vr_license_plate_dev.consumer}
        enabled: ${LICENSE_PLATE_KAFKA_CONSUMERS:false}
  employee-updated-event:
    kafka:
      consumer:
        topic: ${EMPLOYEE_UPDATED_EVENT_TOPIC:FRA_emhs_us_employee_update_topic_dev}
        group-id: ${EMPLOYEE_UPDATED_EVENT_GROUP_ID:FRA_emhs_us_employee_update_topic_dev.consumer}
      enabled: ${EMPLOYEE_UPDATED_EVENT_KAFKA_CONSUMERS:false}
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    # 0 0 0 1 * ?
    planned-vehicle-transfer-initialization-cron: ${PLANNED_VEHICLE_TRANSFER_INITIALIZATION_SCHEDULE:0 0 0 1 1 ? 2099}
    maintenance-order-number-update-cron: ${MAINTENANCE_ORDER_NUMBER_UPDATE_SCHEDULE:0 0 0 1 1 ? 2099}
  # delivery lead time in working days
  delivery-lead-time: ${VEHICLE_TRANSFER_DELIVERY_LEAD_TIME:5}
  delivery-email:
    sender-email-address: ${VEHICLE_TRANSFER_DELIVERY_SENDER_EMAIL_ADDRESS:}

pre-delivery-inspection:
  # pdi lead time in working days
  pdi-lead-time: ${PRE_DELIVERY_INSPECTION_PDI_LEAD_TIME:5}
  pdi-ordering:
    scheduler:
      pdi-ordering-email-cron: ${PDI_ORDERING_EMAIL_SCHEDULE:0 0 0 1 1 ? 2099}
    email:
      recipient-to-email-address: ${PDI_ORDERING_RECIPIENT_TO_EMAIL_ADDRESS:}
      recipient-cc-email-address: ${PDI_ORDERING_RECIPIENT_CC_EMAIL_ADDRESS:}
      sender-email-address: ${PDI_ORDERING_SENDER_EMAIL_ADDRESS:}

mailclient:
  smtp:
    host: ${SMTP_HOST:}
    port: ${SMTP_PORT:465}
    username: ${SMTP_USERNAME:}
    password: ${SMTP_PASSWORD:}
  override-email-recipients: ${MAILCLIENT_OVERRIDE_EMAIL_RECIPIENTS:true}
  override-recipients-list: ${MAILCLIENT_OVERRIDE_EMAIL_RECIPIENTS_LIST:}

msbooking:
  base-url: https://graph.microsoft.com/v1.0
  booking-id: ${BOOKING_ID:<EMAIL>}
  enabled: ${MS_BOOKING_ENABLE:false}
  cancel-timeframe: 30
  service-id: ${SERVICE_ID:870dd2d1-5c83-4ad3-b7e8-48a98942fe2f}
  security:
    username: ${MS_BOOKING_USERNAME:<EMAIL>}
    password: ${MS_BOOKING_PASSWORD}
    client-id: ${MS_BOOKING_CLIENT_CLIENT_ID}
    client-secret: ${MS_BOOKING_CLIENT_CLIENT_SECRET}
    authorization-grant-type: password
    scope: ${MS_BOOKING_CLIENT_SCOPE:https://graph.microsoft.com/.default}
    token-uri: https://login.microsoftonline.com/${ENTRA_ID_AZURE_TENANT_ID}/oauth2/v2.0/token
  questions-id:
    delivery_vin: ${DELIVERY_VIN:4281d90c-84bf-4124-8e2d-adba84806d27}
    delivery_license-plate: ${DELIVERY_LICENSE_PLATE:e062aba4-ab0e-46cc-898e-1ba309e07406}
    delivery_model: ${DELIVERY_MODEL:5710b5b7-e0ee-4f64-97c0-cca28cddfcfb}
    return_vin: ${RETURN_VIN:86df2ffa-49e5-461f-8194-90a4f00263f2}
    return_license-plate: ${RETURN_LICENSE_PLATE:e062aba4-ab0e-46cc-898e-1ba309e07406}
    return_model: ${RETURN_MODEL:5a97db48-f4e1-45cb-94bf-f6b0d188b4c4}
  scheduler:
    msbooking-appointments-cron: ${MS_BOOKING_APPOINTMENTS_JOB_SCHEDULE:0 0/2 * * * ?}

pace:
  base-url: ${PACE_BASE_URL:}

vehicle-history:
  clients:
    vehicle-registration:
      base-url: ${VR_BASE_URL:http://localhost:8081/api/vr}
      client-id: ${VEHICLE_REGISTRATION_HISTORY_API_CLIENT_ID:vr-m2m-archive-client-id}
      client-secret: ${VEHICLE_REGISTRATION_HISTORY_API_CLIENT_SECRET:vr-m2m-archive-client-secret}
      scope: ${VEHICLE_REGISTRATION_HISTORY_API_SCOPE:vr-app-id}

database:
  rds: ${RDS_ENABLED:false}
  region: ${AWS_REGION:eu-west-1}

vehicle-campaigns:
  enabled: ${VEHICLE_CAMPAIGNS_ENABLED:false}
  cleanup-vehicles:
    cron: '0 */2 * * * ?'
  filter-vehicles:
    cron: '0 * * ? * *'
    vehicle-transfer-batch-size: 5
    max-vehicles-to-schedule-per-run: 10
  dmsi:
    base-uri: ${DMSI_BASE_URI:http://localhost:7887/rwil/pag/retail}
    x-personal-id: DEUP01040
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: '30 * * ? * *'

non-customer-adequate-vehicles:
  scheduler:
    evaluation-cron: '0 * * ? * *'

ecc:
  allow-incoming-traffic: ${ECC_ALLOW_INCOMING_TRAFFIC:true}

tire-management:
  enabled: ${TIRE_MANAGEMENT_ENABLED:false}
  data-export:
    cron: ${TIRE_MANAGEMENT_DATA_EXPORT_CRON:0 */5 * ? * *}
  email:
    smtp-host: ${SMTP_HOST:localhost}
    smtp-port: ${SMTP_PORT:1025}
    smtp-username: ${SMTP_USERNAME:}
    smtp-password: ${SMTP_PASSWORD:}
    sender: ${TIRE_MANAGEMENT_EMAIL_SENDER:test@localhost}
    recipient: ${TIRE_MANAGEMENT_EMAIL_RECIPIENT:<EMAIL>}

repair-fix:
  base-url: ${REPAIR_FIX_BASE_URL:}

vehicle-evaluation:
  tuv-commission:
    tuv-email:
      recipient-to-email-address: ${TUV_TEAM_RECIPIENT_TO_EMAIL_ADDRESS:}
      sender-email-address: ${TUV_TEAM_SENDER_EMAIL_ADDRESS:}
    logistics-provider-email:
      recipient-to-email-address: ${LOGISTICS_TEAM_RECIPIENT_TO_EMAIL_ADDRESS:}
      sender-email-address: ${LOGISTICS_TEAM_SENDER_EMAIL_ADDRESS:}

balance-sheet:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    synchronize-scrapping-status-update-cron: ${SYNCHRONIZE_SCRAPPING_STATUS_UPDATE_CRON:0 0 0 1 1 ? 2099}
    cost-center-update-cron: ${COST_CENTER_UPDATE_CRON:0 0 0 1 1 ? 2099}
    synchronize-blocked-for-sale-update-cron: ${SYNCHRONIZE_BLOCKED_FOR_SALE_UPDATE_CRON:0 0 0 1 1 ? 2099}
    cost-center-update-preparation-cron: ${COST_CENTER_UPDATE_PREPARATION_CRON:0 0 0 1 1 ? 2099}
    prepare-blocked-for-sale-update-cron: ${PREPARE_BLOCKED_FOR_SALE_UPDATE_CRON:0 0 0 1 1 ? 2099}
  pace:
    base-url: ${PACE_BALANCE_SHEET_BASE_URL:}

dms-vehicle-migration:
  kafka-producer:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:29092}
    key-serializer: org.apache.kafka.common.serialization.StringSerializer
    value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    sasl-mechanism: PLAIN
    sasl-jaas-config: org.apache.kafka.common.security.plain.PlainLoginModule required username="${KAFKA_USER}" password="${KAFKA_PASSWORD}";
    security-protocol: ${KAFKA_SECURITY_PROTOCOL:PLAINTEXT}
    session-timeout-ms: 45000
  topic: ${DMS_VEHICLE_MIGRATION_TOPIC:FRA_emhs_dms_vehicle_migration_dev}
  enabled: ${DMS_VEHICLE_MIGRATION_ENABLED:false}

carsync:
  enabled: ${ENABLE_CARSYNC:false}
  password: ${CARSYNC_USER_PASSWORD}
  username: ${CARSYNC_USER_USERNAME:<EMAIL>}
  base-url: ${CARSYNC_BASE_URL:https://porsche-staging.carsync-log.de/api/}
  certificate:
    keystore: ${CARSYNC_KEYSTORE}
    truststore: ${CARSYNC_TRUSTSTORE}
  scheduler:
    sync-mileage-job-cron: ${SYNC_MILEAGE_CRON:0 0 0 1 1 ? 2099}

vehicle-sales:
  b2b-integration:
    enabled: ${VEHICLE_SALES_B2B_INTEGRATION_ENABLED:false}
    consumer:
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
      schema-registry-url: ${KAFKA_SCHEMA_REGISTRY_URL:https://psrc-8vyvr.eu-central-1.aws.confluent.cloud}
      schema-registry-user: ${KAFKA_SCHEMA_REGISTRY_USER:HPKMESFZJFBWJZ56}
      schema-registry-password: ${KAFKA_SCHEMA_REGISTRY_PASSWORD}
      group-id: ${KAFKA_B2B_CONSUMER_GROUP_ID:FRA_emhs_honeypotters_completed_auctions_dev}

vtstamm:
  allow-incoming-traffic: ${VTSTAMM_ALLOW_INCOMING_TRAFFIC:true}
