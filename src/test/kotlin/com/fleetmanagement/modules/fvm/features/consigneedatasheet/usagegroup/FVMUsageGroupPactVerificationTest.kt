/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.fvm.features.consigneedatasheet.usagegroup

import au.com.dius.pact.provider.junit5.PactVerificationContext
import au.com.dius.pact.provider.junit5.PactVerificationInvocationContextProvider
import au.com.dius.pact.provider.junitsupport.Consumer
import au.com.dius.pact.provider.junitsupport.Provider
import au.com.dius.pact.provider.junitsupport.State
import au.com.dius.pact.provider.junitsupport.loader.PactFolder
import au.com.dius.pact.provider.spring.junit5.MockMvcTestTarget
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupApplicationService
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupFinder
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ApiLabelConstant
import com.fleetmanagement.modules.fvm.objectmothers.VehicleTransferMaintenanceObjectMother
import com.fleetmanagement.pact.Pacticipants.FLEET_MANAGEMENT_UI
import com.fleetmanagement.pact.Pacticipants.USAGE_GROUP
import com.fleetmanagement.security.api.dto.PrivilegePermission.READ
import com.fleetmanagement.security.api.dto.PrivilegePermission.WRITE
import com.fleetmanagement.security.configurations.SecurityConfiguration
import com.fleetmanagement.security.features.accesscontrol.domain.Privilege
import com.fleetmanagement.security.features.accesscontrol.services.PrivilegeService
import com.fleetmanagement.security.features.tokenvalidation.JwtValidator
import com.fleetmanagement.security.utils.WithMockALBUser
import com.ninjasquad.springmockk.MockkBean
import io.mockk.clearAllMocks
import io.mockk.every
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestTemplate
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.converter.HttpMessageConverter

@WebMvcTest(
    controllers = [FVMUsageGroupController::class],
    properties = ["security.enabled=true", "pact_do_not_track=true"],
    excludeAutoConfiguration = [JpaRepositoriesAutoConfiguration::class],
)
@Import(SecurityConfiguration::class, ApiLabelConstant::class)
@AutoConfigureMockMvc
@Provider(USAGE_GROUP)
@Consumer(FLEET_MANAGEMENT_UI)
@PactFolder("pacts/provider")
class FVMUsageGroupPactVerificationTest {
    @MockkBean
    private lateinit var usageGroupApplicationService: UsageGroupApplicationService

    @MockkBean
    private lateinit var usageGroupFinder: UsageGroupFinder

    @MockkBean(name = "albTokenValidator")
    private lateinit var albTokenValidator: JwtValidator

    @MockkBean(name = "apiGatewayTokenValidator")
    private lateinit var apiGatewayTokenValidator: JwtValidator

    @MockkBean
    private lateinit var privilegeService: PrivilegeService

    @Autowired
    private lateinit var controller: FVMUsageGroupController

    @Autowired
    private lateinit var httpMessageConverters: List<HttpMessageConverter<*>>

    @BeforeEach
    fun before(context: PactVerificationContext) {
        context.target =
            MockMvcTestTarget(
                controllers = listOf(controller),
                messageConverters = httpMessageConverters,
                servletPath = "/vs",
            )
        every { albTokenValidator.validate(any()) } returns Unit
        every { albTokenValidator.canValidate(any()) } returns true

        every { apiGatewayTokenValidator.validate(any()) } returns Unit
        every { apiGatewayTokenValidator.canValidate(any()) } returns true

        every { privilegeService.findAllByGroupIdWithFilters(any()) } returns
            listOf(
                Privilege(id = 1000, resource = "usageGroup", permission = READ, filter = null),
                Privilege(id = 1001, resource = "usageGroup", permission = WRITE, filter = null),
            )
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
    }

    @State("usage group data exists")
    fun setupReadAllUsageGroup() {
        every { usageGroupFinder.readAllUsageGroups() } returns
            (1..5).map { VehicleTransferMaintenanceObjectMother.usageGroup() }
    }

    @TestTemplate
    @WithMockALBUser(
        authorities = [
            ApiLabelConstant.API_USAGE_GROUP_READ,
            ApiLabelConstant.API_USAGE_GROUP_WRITE,
            ApiLabelConstant.API_USAGE_GROUP_DELETE,
        ],
    )
    @ExtendWith(PactVerificationInvocationContextProvider::class)
    fun pactVerificationTestTemplate(context: PactVerificationContext) {
        context.verifyInteraction()
    }
}
