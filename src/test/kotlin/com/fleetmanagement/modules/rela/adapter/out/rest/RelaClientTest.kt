/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.rela.adapter.out.rest

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.rela.adapter.out.rest.model.CreateAppointmentResponseDto
import com.fleetmanagement.modules.rela.application.RelaAppointmentCancellationException
import com.fleetmanagement.modules.rela.application.RelaAppointmentCreationException
import com.fleetmanagement.modules.rela.application.RelaAppointmentRequest
import com.fleetmanagement.modules.rela.application.RelaCancellationRequest
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.getAllServeEvents
import com.github.tomakehurst.wiremock.client.WireMock.ok
import com.github.tomakehurst.wiremock.client.WireMock.okJson
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.stubFor
import com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo
import com.github.tomakehurst.wiremock.junit5.WireMockTest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.containsString
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.TestPropertySource
import java.time.LocalDate
import java.time.OffsetDateTime

@WireMockTest(httpPort = 8093)
@SpringBootTest
@Import(TestcontainersConfiguration::class)
@TestPropertySource(
    properties = [
        "rela.enabled=true",
        "rela.base-url=http://localhost:8093",
        "rela.api-key=test-api-key",
    ],
)
class RelaClientTest {
    private val objectMapper = ObjectMapper().registerModules(JavaTimeModule())

    @Autowired
    private lateinit var relaClient: RelaClient

    @Test
    fun `should create appointment successfully`() {
        val request =
            RelaAppointmentRequest(
                appointmentDate = OffsetDateTime.now().plusDays(7),
                appointmentTime = OffsetDateTime.now().plusDays(7).withHour(10),
                vehicleLicensePlate = "S-PL 1234",
                vehicleVin = "WP0ZZZ999SS123456",
                customerLastName = "Müller",
                customerFirstName = "Hans",
                serviceBayNumber = 1,
                serviceTypeId = 2,
            )

        val expectedResponse = CreateAppointmentResponseDto(auftragsnummer = "14274000023")

        stubFor(
            post(urlPathEqualTo("/rela-rest-ws/luckyneutron/appointments"))
                .willReturn(okJson(objectMapper.writeValueAsString(expectedResponse))),
        )

        val result = relaClient.createAppointment(request)

        assertEquals("14274000023", result.orderNumber)

        val serverEvents = getAllServeEvents()
        assertEquals(1, serverEvents.size)
        val appointmentRequest = serverEvents[0].request
        assertThat(appointmentRequest.url, containsString("/rela-rest-ws/luckyneutron/appointments"))
    }

    @Test
    fun `should throw exception when appointment creation fails`() {
        val request =
            RelaAppointmentRequest(
                appointmentDate = OffsetDateTime.now().plusDays(7),
                appointmentTime = OffsetDateTime.now().plusDays(7).withHour(10),
                vehicleLicensePlate = "S-PL 1234",
                vehicleVin = "WP0ZZZ999SS123456",
                customerLastName = "Müller",
                customerFirstName = "Hans",
            )

        stubFor(
            post(urlPathEqualTo("/rela-rest-ws/luckyneutron/appointments"))
                .willReturn(
                    aResponse()
                        .withStatus(500)
                        .withHeader("Content-Type", "application/json")
                        .withBody("Internal Server Error"),
                ),
        )

        assertThrows<RelaAppointmentCreationException> {
            relaClient.createAppointment(request)
        }
    }

    @Test
    fun `should cancel appointment successfully`() {
        val orderNumber = "14274000023"
        val cancellationRequest =
            RelaCancellationRequest(
                cancelledBy = "Hans Müller",
                cancellationDate = LocalDate.now(),
            )

        stubFor(
            post(urlPathEqualTo("/rela-rest-ws/luckyneutron/appointments/$orderNumber/cancelation"))
                .willReturn(ok()),
        )

        relaClient.cancelAppointment(orderNumber, cancellationRequest)

        val serverEvents = getAllServeEvents()
        assertEquals(1, serverEvents.size)
        val cancellationRequestEvent = serverEvents[0].request
        assertThat(
            cancellationRequestEvent.url,
            containsString("/rela-rest-ws/luckyneutron/appointments/$orderNumber/cancelation"),
        )
    }

    @Test
    fun `should throw exception when appointment cancellation fails`() {
        val orderNumber = "14274000023"
        val cancellationRequest =
            RelaCancellationRequest(
                cancelledBy = "Hans Müller",
                cancellationDate = LocalDate.now(),
            )

        stubFor(
            post(urlPathEqualTo("/rela-rest-ws/luckyneutron/appointments/$orderNumber/cancelation"))
                .willReturn(
                    aResponse()
                        .withStatus(500)
                        .withHeader("Content-Type", "application/json")
                        .withBody("Internal Server Error"),
                ),
        )

        assertThrows<RelaAppointmentCancellationException> {
            relaClient.cancelAppointment(orderNumber, cancellationRequest)
        }
    }

    @Test
    fun `should use correct authorization header`() {
        val request =
            RelaAppointmentRequest(
                appointmentDate = OffsetDateTime.now().plusDays(7),
                appointmentTime = OffsetDateTime.now().plusDays(7).withHour(10),
                vehicleLicensePlate = "S-PL 1234",
                vehicleVin = "WP0ZZZ999SS123456",
                customerLastName = "Müller",
                customerFirstName = "Hans",
                serviceBayNumber = 1,
                serviceTypeId = 2,
            )

        val expectedResponse = CreateAppointmentResponseDto(auftragsnummer = "14274000023")

        stubFor(
            post(urlPathEqualTo("/rela-rest-ws/luckyneutron/appointments"))
                .willReturn(okJson(objectMapper.writeValueAsString(expectedResponse))),
        )

        relaClient.createAppointment(request)

        val serverEvents = getAllServeEvents()
        assertEquals(1, serverEvents.size)
        val appointmentRequest = serverEvents[0].request

        assertThat(appointmentRequest.url, containsString("/rela-rest-ws/luckyneutron/appointments"))

        assertEquals(
            "Rela-Token test-api-key",
            appointmentRequest.headers
                .getHeader("Authorization")
                .values()
                .single(),
        )
    }
}
