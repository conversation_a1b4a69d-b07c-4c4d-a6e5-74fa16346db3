/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment

import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntry
import com.fleetmanagement.modules.vehicletransfer.charPool
import java.time.OffsetDateTime
import kotlin.random.Random

class TireChangeEntryBuilder {
    private var msBookingsAppointmentId: String = createRandomString(Random.nextInt(4, 23))
    private var msBookingsAppointmentDate: OffsetDateTime = OffsetDateTime.now()
    private var licensePlate: String = createRandomString(Random.nextInt(4, 23))
    private var firstName: String = createRandomString(Random.nextInt(4, 23))
    private var lastName: String = createRandomString(Random.nextInt(4, 23))
    private var vin: String = createRandomString(Random.nextInt(4, 23))
    private var shouldUpdateRela: Boolean = Random.nextBoolean()
    private var relaOrderNumber: String = createRandomString(Random.nextInt(4, 23))

    fun build(): TireChangeEntry =
        TireChangeEntry(
            msBookingsAppointmentId = msBookingsAppointmentId,
            msBookingsAppointmentDate = msBookingsAppointmentDate,
            licensePlate = licensePlate,
            firstName = firstName,
            lastName = lastName,
            vin = vin,
            shouldUpdateRela = shouldUpdateRela,
            relaOrderNumber = relaOrderNumber,
        )

    companion object {
        fun buildSingle() = TireChangeEntryBuilder().build()

        fun buildMultiple(count: Int): Set<TireChangeEntry> = (1..count).map { TireChangeEntryBuilder().build() }.toSet()
    }
}

fun createRandomString(length: Int): String = (1..length).map { Random.nextInt(0, charPool.size) }.map(charPool::get).joinToString("")
