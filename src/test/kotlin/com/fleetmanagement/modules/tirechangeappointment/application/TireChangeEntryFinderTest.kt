/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.application

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.tirechangeappointment.TireChangeEntryBuilder
import com.fleetmanagement.modules.tirechangeappointment.domain.TireChangeEntryRepository
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.`is`
import org.hamcrest.Matchers.samePropertyValuesAs
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.transaction.annotation.Transactional

@SpringBootTest
@Transactional
@Import(TestcontainersConfiguration::class)
class TireChangeEntryFinderTest {
    @Autowired
    private lateinit var tireChangeEntryFinder: TireChangeEntryFinder

    @Autowired
    private lateinit var tireChangeEntryRepository: TireChangeEntryRepository

    @Test
    fun `should store tire change entry and find it`() {
        val tireChangeEntry = TireChangeEntryBuilder.buildSingle()
        tireChangeEntryRepository.save(tireChangeEntry)
        val tireChangeEntryFromFinder = tireChangeEntryFinder.getTireChangeEntryById(tireChangeEntry.id)

        assertThat(tireChangeEntryFromFinder, `is`(samePropertyValuesAs(tireChangeEntry)))
    }
}
