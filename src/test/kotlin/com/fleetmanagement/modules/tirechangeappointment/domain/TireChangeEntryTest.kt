/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tirechangeappointment.domain

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNotNull
import java.time.OffsetDateTime
import kotlin.random.Random

class TireChangeEntryTest {
    @Test
    fun `should initialise TireChangeEntry`() {
        val msBookingsAppointmentId = "1234"
        val msBookingsAppointmentDate = OffsetDateTime.now()
        val licensePlate = "BBN1234"
        val firstName = "John"
        val lastName = "Doe"
        val vin = "sample-vin"
        val shouldUpdateRela = Random.nextBoolean()
        val relaOrderNumber = "ordernumber"

        val tireChangeEntry =
            TireChangeEntry(
                msBookingsAppointmentId = msBookingsAppointmentId,
                msBookingsAppointmentDate = msBookingsAppointmentDate,
                licensePlate = licensePlate,
                firstName = firstName,
                lastName = lastName,
                vin = vin,
                shouldUpdateRela = shouldUpdateRela,
                relaOrderNumber = relaOrderNumber,
            )

        assertEquals(msBookingsAppointmentId, tireChangeEntry.msBookingsAppointmentId)
        assertEquals(msBookingsAppointmentDate, tireChangeEntry.msBookingsAppointmentDate)
        assertEquals(licensePlate, tireChangeEntry.licensePlate)
        assertEquals(firstName, tireChangeEntry.firstName)
        assertEquals(lastName, tireChangeEntry.lastName)
        assertEquals(vin, tireChangeEntry.vin)
        assertEquals(shouldUpdateRela, tireChangeEntry.shouldUpdateRela)
        assertEquals(relaOrderNumber, tireChangeEntry.relaOrderNumber)
        assertNotNull(tireChangeEntry.created)
        assertEquals("", tireChangeEntry.createdBy)
        assertNotNull(tireChangeEntry.lastModified)
        assertEquals("", tireChangeEntry.lastModifiedBy)
    }
}
