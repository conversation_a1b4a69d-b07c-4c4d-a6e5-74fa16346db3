env                                                  = "dev"
firehose_cloudwatch_role_name                        = "vehicle-service-firehose-cloudwatch-role"
firehose_s3_role_name                                = "vehicle-service-firehose-s3-role"
vehicle_service_domain                               = "vs.mobilityservicesdev.aws.platform.porsche-preview.cloud"
dns_hosted_zone_id                                   = "Z03473891MTFTKDN45WAD"
dns_hosted_zone_name                                 = "mobilityservicesdev.aws.platform.porsche-preview.cloud"
app_domain                                           = "mobilityservicesdev.porsche.services"
oidc_token_url                                       = "https://identity-sandbox.vwgroup.io/oidc/v1/token"
pvh_base_url                                         = "https://api.staging.emea.gravity.porsche.com/prod-business-asdcs-vehicle-service"
pvh_client_id                                        = "60780012-5361-487b-8c4f-0472c9b35307@apps_vw-dilab_com"
oauth2_issuer_uri                                    = "https://login.microsoftonline.com/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4/v2.0"
oauth2_token_uri                                     = "https://login.microsoftonline.com/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4/oauth2/v2.0"
oauth2_vehicle_audience                              = "1d612e2b-317e-4d59-80f5-f80b3e3a33c0"
oauth2_dm_audience                                   = "de2c475e-e393-425d-a89e-a1f394c90ae1"
kafka_user                                           = "SW42RJER6HGHBWY6"
kafka_enable_pvh_ingestion                           = true
kafka_pvh_stop_on_error                              = false
kafka_pvh_consumer_group_id                          = "FRA_emhs_pvh_Vehicle_Embargo_01_2025_DEV"
timestamp_filtering_disable_after                    = "2024-11-01T08:00:00Z"
kafka_bootstrap_servers                              = "pkc-rxgnk.eu-west-1.aws.confluent.cloud:9092"
kafka_auto_offset_reset                              = "latest"
enable_albheaders_endpoint_for_testing               = "true"
vehicle_registration_base_uri                        = "http://vehicle-registration-server.emh-fleet-management:8080"
vehicle_registration_base_url                        = "http://vehicle-registration-server.emh-fleet-management:8080/api/vr"
vehicle_location_base_url                            = "https://ls.mobilityservicesdev.aws.platform.porsche-preview.cloud"
logout_uri                                           = "https://login.microsoftonline.com/07f8138c-e3e4-4b45-842b-9ecc3ba58cf4/oauth2/v2.0/logout"
oauth2_vehicle_registration_app_id                   = "cfb99f74-19b0-4747-b96f-81989c59bb4a"
allow_overwrite_of_groups_from_request_header        = true
retention_in_days                                    = 1
s3_bucket_archive                                    = "vehicle-service-archive-938255451121"
s3_bucket_legal_hold                                 = "vehicle-service-legal-hold-938255451121"
s3_bucket_vehicle_sales_legal_hold                   = "vehicle-service-sales-legal-hold-938255451121"
fms_migration_enabled                                = true
s3_bucket_fms_migration                              = "vehicle-service-fms-migration-938255451121"
s3_bucket_vehicle_transfer_archive                   = "vehicle-transfer-service-archive-938255451121"
s3_bucket_vehicle_history_archive                    = "vehicle-service-history-archive-938255451121"
s3_bucket_vehicle_sales_archive                      = "vehicle-service-sales-archive-938255451121"
vehicle_registration_client_id                       = "d594f124-a777-41ca-b09b-9af3def7379a"
vehicle_registration_archive_client_id               = "d594f124-a777-41ca-b09b-9af3def7379a"
vehicle_registration_legalhold_client_id             = "d594f124-a777-41ca-b09b-9af3def7379a"
vehicle_archive_cron_schedule                        = "0 0 1 * * ?"
vehicle_archive_enable_scheduler                     = "true"
cost_center_update_cron_schedule                     = "0 0 0 1 1 ? 2099"
cost_center_update_preparation_cron_schedule         = "0 0 0 1 1 ? 2099"
object_location_updated_topic_group_id               = "FRA_emhs_vls_object_location_event.dev.consumer"
object_location_updated_topic                        = "FRA_emhs_vls_object_location_event_dev"
vehicle_location_vls_kafka_integration_enabled       = true
vehicle_location_client_id                           = "668b613f-e81e-4cf4-b34f-c70b118430ea"
vehicle_location_scope                               = "6377c8e7-723e-41ec-b563-ac0fdbecbf43/.default"
vehicle_location_azure_tenant_id                     = "56564e0f-83d3-4b52-92e8-a6bb9ea36564"
location_updated_topic_group_id                      = "FRA_emhs_vls_location_updated_event.dev.consumer"
location_updated_topic                               = "FRA_emhs_vls_location_updated_event_dev"
location_deleted_topic_group_id                      = "FRA_emhs_vls_location_deleted_event.dev.consumer"
location_deleted_topic                               = "FRA_emhs_vls_location_deleted_event_dev"
employee_base_url                                    = "https://user.mobilityservicesdev.aws.platform.porsche-preview.cloud/api"
employee_api_client_id                               = "6f628c98-63e9-4ea0-ad3a-a8433ffb8aca"
employee_api_scope                                   = "1a6442c4-e25f-4459-9a7a-8e7a14386bae/.default"
employee_oauth_token_uri                             = "https://login.microsoftonline.com/56564e0f-83d3-4b52-92e8-a6bb9ea36564/oauth2/v2.0/token"
cap_oauth2_client_id                                 = "df5d1b6a-2e6d-4053-ba20-bc3ab6b460ac"
ppn_host                                             = "ppnlite.porsche.com"
cap_ibm_api_gateway_client_id                        = "8d81e088c8b68810f3e32ba025dc4a7f"
cap_base_url                                         = "https://eu-0.test.api.porsche.io/porsche-group/test/crm"
cap_region                                           = "europe"
planned_vehicle_transfer_initialization_schedule     = "0 5 * * * ?"
feature_flags_read_vehicle_person_enabled            = true
container_cpu                                        = 1024
container_memory                                     = 2048
license_plate_topic                                  = "FRA_emhs_vr_license_plate_dev"
license_plate_group_id                               = "FRA_emhs_vr_license_plate_dev.consumer.dev"
license_plate_kafka_consumers                        = true
entra_id_azure_tenant_id                             = "07f8138c-e3e4-4b45-842b-9ecc3ba58cf4"
enable_entra_id_scheduler                            = true
entra_id_administrative_unit_id                      = "6bbcd412-527e-4e4d-89c1-5862b4e5d239"
entra_id_app_role_assignment_id                      = "6f9c0024-186a-43d9-aa3a-cbcb8c9067d0"
entra_id_sync_cron_schedule                          = "0 */10 * * * ?"
entra_id_oauth2_client_id                            = "4025ad4f-5367-46cb-ae91-05e21d176efe"
vehicle_transfer_delivery_lead_time                  = 5
pre_delivery_inspection_lead_time                    = 5
number_of_damages_topic                              = "FRA_emhs_dms_numer_of_damages_topic_dev"
number_of_damages_group_id                           = "FRA_emhs_dms_numer_of_damages_topic_dev.consumer.dev"
number_of_damages_enable                             = true
pdi_ordering_email_schedule                          = "0 0 0 1 1 ? 2099"
pdi_ordering_recipient_to_email_address              = ""
pdi_ordering_recipient_cc_email_address              = ""
pdi_ordering_sender_email_address                    = ""
vehicle_transfer_delivery_sender_email_address       = ""
smtp_host                                            = ""
smtp_port                                            = 465
smtp_username                                        = ""
dmsi_base_uri                                        = "https://rwil-qa.volkswagenag.com/rwil/pag/retail"
rwil_idp_host                                        = "idp.rwil.qa.eu.bp.aws.cloud.vwgroup.com"
dmsi_oauth2_client_id                                = "DEUP50050_OTHER"
vehicle_campaigns_enabled                            = false
vehicle_status_recalculation_cron                    = "0 0 2 * * ?"
ms_booking_client_client_id                          = "602297c1-a226-48ca-98f7-87c242ae530c"
ms_booking_client_scope                              = "https://graph.microsoft.com/.default"
ms_booking_appointments_job_schedule                 = "0 0/2 * * * ?"
booking_id                                           = "<EMAIL>"
service_id                                           = "870dd2d1-5c83-4ad3-b7e8-48a98942fe2f"
msbooking_questions_id_delivery_vin                  = "4281d90c-84bf-4124-8e2d-adba84806d27"
msbooking_questions_id_delivery_license_plate        = "e062aba4-ab0e-46cc-898e-1ba309e07406"
msbooking_questions_id_delivery_model                = "5710b5b7-e0ee-4f64-97c0-cca28cddfcfb"
msbooking_questions_id_return_vin                    = "86df2ffa-49e5-461f-8194-90a4f00263f2"
msbooking_questions_id_return_license_plate          = "e062aba4-ab0e-46cc-898e-1ba309e07406"
msbooking_questions_id_return_model                  = "5a97db48-f4e1-45cb-94bf-f6b0d188b4c4"
msbooking_enable                                     = false
msbooking_username                                   = "<EMAIL>"
oauth2_client_id_for_pace                            = "sb-f39ece74-12bd-4f49-87c3-8eb9e20d37fe!b139374|it-rt-porsche-qas-spine-integration!b117912"
oauth2_host_pace                                     = "porsche-qas-spine-integration.authentication.eu10.hana.ondemand.com"
pace_base_url                                        = "https://porsche-qas-spine-integration.it-cpi018-rt.cfapps.eu10-003.hana.ondemand.com"
employee_updated_event_topic                         = "FRA_emhs_us_employee_update_topic_dev"
employee_updated_event_group_id                      = "FRA_emhs_us_employee_update_topic_dev.consumer.dev"
employee_updated_event_enabled                       = true
maintenance_order_number_update_schedule             = "0 0 0 1 1 ? 2099"
fleet_master_data_topic                              = "FRA_emhs_fleet_master_vehicle_dev"
enable_fleet_master_data                             = true
fleet_master_data_cron_schedule                      = "0 * * * * ?"
kafka_schema_registry_user                           = "HPKMESFZJFBWJZ56"
kafka_schema_registry_url                            = "https://psrc-8vyvr.eu-central-1.aws.confluent.cloud"
kafka_schema_auto_register                           = false
factory_car_preparation_order_number_update_schedule = "0 0 0 1 1 ? 2099"
tire_management_enabled                              = false
tire_management_data_export_cron                     = "0 0 1 * * ?"
tire_management_email_recipient                      = "<EMAIL>"
tire_management_email_sender                         = "<EMAIL>"
repairfix_base_url                                   = "https://portal.staging.motum.eu"
tuv_team_recipient_to_email_address                  = ""
tuv_team_sender_email_address                        = ""
logistics_team_recipient_to_email_address            = ""
logistics_team_sender_email_address                  = ""
synchronize_scrapping_status_update_cron             = "0 0 0 1 1 ? 2099"
ecc_allow_incoming_traffic                           = false
kafka_pia_consumer_group_id                          = "FRA_emhs_one_vms_piaom_importer_invoice_dev"
enable_kafka_pia_integration                         = true
kafka_pia_auto_offset_reset                          = "earliest"
synchronize_blocked_for_sale_update_cron             = "0 0 0 1 1 ? 2099"
prepare_blocked_for_sale_update_cron                 = "0 0 0 1 1 ? 2099"
vehicle_transfer_allow_incoming_traffic              = false
kafka_financial_asset_type_consumer_group_id         = "FRA_emhs_p40_account_2_report_fleet_vehicle_manager_asset_class_dev"
enable_kafka_p40_integration                         = true
kafka_financial_asset_type_auto_offset_reset         = "earliest"
dms_vehicle_migration_topic                          = "FRA_emhs_dms_vehicle_migration_dev"
oauth2_client_id_for_pace_balance_sheet              = "sb-e94825d5-b482-4e63-8245-e5de4fc8e4bf!b138742|it-rt-porsche-dev-spine-integration!b117912"
oauth2_host_pace_balance_sheet                       = "porsche-dev-spine-integration.authentication.eu10.hana.ondemand.com"
pace_balance_sheet_base_url                          = "https://porsche-dev-spine-integration.it-cpi018-rt.cfapps.eu10-003.hana.ondemand.com"
dms-vehicle-migration-enabled                        = true
carsync_enabled                                      = true
carsync_username                                     = "<EMAIL>"
carsync_base_url                                     = "https://porsche-staging.carsync-log.de/api/"
sync_mileage_cron                                    = "0 0 0 * * ? *"
financial_asset_type_update_sync_cron                = "0 0 0 1 1 ? 2099"
vtstamm_allow_incoming_traffic                       = true
mailclient_override_email_recipients                 = true
mailclient_override_recipients_list                  = "<EMAIL>, <EMAIL>"
vehicle_sales_b2b_integration_enabled                = true
kafka_b2b_consumer_group_id                          = "fra_emhs_honeypotters_completed_auctions_dev"
rela_enabled                                         = false
rela_base_url                                        = "https://rela.sdnord.de"
